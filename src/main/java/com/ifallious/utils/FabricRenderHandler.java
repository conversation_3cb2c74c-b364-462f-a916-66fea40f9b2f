package com.ifallious.utils;
import com.mojang.blaze3d.systems.RenderSystem;
import gg.essential.universal.UMinecraft;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.render.Camera;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec3d;

import static com.ifallious.Wynnutils.mc;

public class FabricRenderHandler {
    public static void initialize() {
        TextRenderer3D.initialize();
        // Try this stage instead
        WorldRenderEvents.END.register(context -> {
            //RenderUtils.renderText(context.matrixStack());
            TextRenderer3D.renderAll(context.matrixStack());
        });
        HudRenderCallback.EVENT.register((context, tickDelta) -> {

        });
    }
}
