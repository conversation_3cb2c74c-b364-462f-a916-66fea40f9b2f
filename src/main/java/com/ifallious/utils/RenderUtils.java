package com.ifallious.utils;

import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.systems.RenderSystem;
import gg.essential.universal.UGraphics;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.render.Camera;
import net.minecraft.client.render.Tessellator;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.debug.DebugRenderer;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.AffineTransformation;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import org.joml.Matrix3fc;
import org.joml.Matrix4f;

import java.awt.*;

public class RenderUtils {
    public static void renderText(MatrixStack matrixStack) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.world == null || client.player == null) return;

        // Example position for the text
        double x = client.player.getX() + 2;
        double y = client.player.getY();
        double z = client.player.getZ() + 2;

        Vec3d cameraPos = client.gameRenderer.getCamera().getPos();
        // Translate the matrix to the text's position
        matrixStack.push();
        matrixStack.translate(x - cameraPos.x, y - cameraPos.y, z - cameraPos.z);

        // Scale the text (optional)
        float scale = 0.02f; // Adjust scale as needed
        matrixStack.scale(scale, scale, scale);

        // Render the text
        TextRenderer textRenderer = client.textRenderer;
        String text = "Hello, World!";
        int color = 0xFFFFFF; // White color
        matrixStack.translate(0, 0, 0); // Adjust position if needed
        textRenderer.draw(text, 0, 0, color, false, matrixStack.peek().getPositionMatrix(), client.getBufferBuilders().getEntityVertexConsumers(), TextRenderer.TextLayerType.NORMAL, 0, 0xf000f0);

        matrixStack.pop();
    }
}
