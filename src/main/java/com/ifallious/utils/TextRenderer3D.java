package com.ifallious.utils;

import gg.essential.universal.UChat;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.render.LightmapTextureManager;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.Position;
import net.minecraft.util.math.Vec3d;
import org.joml.Matrix4f;
import com.mojang.blaze3d.systems.RenderSystem;
import org.lwjgl.opengl.GL11;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class TextRenderer3D {
    private static final Map<String, Text3D> activeTexts = new ConcurrentHashMap<>();
    private static boolean isInitialized = false;

    public static class Text3D {
        public String text;
        public double x, y, z;
        public int color;
        public boolean renderBlackBox;
        public float scale;
        public boolean increase;
        public boolean centered;
        public boolean renderThroughBlocks;
        public long duration; // -1 for permanent
        public long startTime;

        public Text3D(String text, double x, double y, double z, int color,
                      boolean renderBlackBox, float scale, boolean increase,
                      boolean centered, boolean renderThroughBlocks, long duration) {
            this.text = text;
            this.x = x;
            this.y = y;
            this.z = z;
            this.color = color;
            this.renderBlackBox = renderBlackBox;
            this.scale = scale;
            this.increase = increase;
            this.centered = centered;
            this.renderThroughBlocks = renderThroughBlocks;
            this.duration = duration;
            this.startTime = System.currentTimeMillis();
        }

        public boolean isExpired() {
            return duration != -1 && (System.currentTimeMillis() - startTime) > duration;
        }
    }

    // Initialize the renderer (call this in your mod's initialization)
    public static void initialize() {
        if (!isInitialized) {
            // Register render event - this depends on your mod loader
            // For Fabric: ClientTickEvents.END_WORLD_TICK.register(world -> renderAll());
            // For Forge: MinecraftForge.EVENT_BUS.register(new RenderEventHandler());
            isInitialized = true;
        }
    }

    // Add permanent text
    public static String addText(String id, String text, double x, double y, double z) {
        return addText(id, text, x, y, z, 0xFFFFFFFF, true, 1.0f, false, true, false, -1);
    }

    // Add text with color
    public static String addText(String id, String text, double x, double y, double z, int color) {
        return addText(id, text, x, y, z, color, true, 1.0f, false, true, true, -1);
    }

    // Add temporary text (duration in milliseconds)
    public static String addText(String id, String text, double x, double y, double z,
                                 int color, long duration) {
        return addText(id, text, x, y, z, color, true, 1.0f, false, true, true, duration);
    }

    // Full customization
    public static String addText(String id, String text, double x, double y, double z,
                                 int color, boolean renderBlackBox, float scale,
                                 boolean increase, boolean centered, boolean renderThroughBlocks,
                                 long duration) {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }

        Text3D text3D = new Text3D(text, x, y, z, color, renderBlackBox, scale,
                increase, centered, renderThroughBlocks, duration);
        activeTexts.put(id, text3D);
        return id;
    }

    // Remove specific text
    public static void removeText(String id) {
        activeTexts.remove(id);
    }

    // Clear all text
    public static void clearAll() {
        activeTexts.clear();
    }

    // Update existing text
    public static void updateText(String id, String newText) {
        Text3D text3D = activeTexts.get(id);
        if (text3D != null) {
            text3D.text = newText;
        }
    }

    // Update text position
    public static void updatePosition(String id, float x, float y, float z) {
        Text3D text3D = activeTexts.get(id);
        if (text3D != null) {
            text3D.x = x;
            text3D.y = y;
            text3D.z = z;
        }
    }

    // This should be called every render frame
    public static void renderAll(MatrixStack matrixStack) {
        MinecraftClient mc = MinecraftClient.getInstance();
        if (mc.world == null || mc.player == null) return;
        // Remove expired texts
        activeTexts.entrySet().removeIf(entry -> entry.getValue().isExpired());

        // Render all active texts
        for (Text3D text3D : activeTexts.values()) {
            renderSingleText(text3D, matrixStack);
        }
    }

    private static void renderSingleText(Text3D text3D, MatrixStack matrixStack) {
        MinecraftClient mc = MinecraftClient.getInstance();

        TextSplitResult splitResult = splitText(text3D.text);
        List<String> lines = splitResult.lines;
        float width = splitResult.width;
        float height = splitResult.height;

        TextRenderer fontRenderer = mc.textRenderer;
        Vec3d cameraPos = mc.gameRenderer.getCamera().getPos();
        Vec3d renderPos = new Vec3d(
                text3D.x - cameraPos.x,
                text3D.y - cameraPos.y,
                text3D.z - cameraPos.z
        );

        float baseScale = 0.025f;
        float finalScale = text3D.scale * (text3D.increase ?
                Math.max(baseScale, (float)(renderPos.distanceTo(cameraPos) / 120.0)) : baseScale);

        matrixStack.push();
        matrixStack.translate(renderPos.x, renderPos.y, renderPos.z);
        matrixStack.multiply(mc.gameRenderer.getCamera().getRotation());
        //matrixStack.scale(0.5f, 0.5f, 0.5f);

        VertexConsumerProvider.Immediate vertexConsumers = mc.getBufferBuilders().getEntityVertexConsumers();
        float offsetY = -(fontRenderer.fontHeight * lines.size()) / 2f;

        for (String line : lines) {
            Matrix4f matrix = matrixStack.peek().getPositionMatrix();

            if (text3D.renderBlackBox) {

            }

            int offsetX = text3D.centered ? -fontRenderer.getWidth(line) / 2 : 0;

            try {
                fontRenderer.draw(
                        line,
                        offsetX,
                        offsetY,
                        text3D.color,
                        false,
                        matrix,
                        vertexConsumers,
                        TextRenderer.TextLayerType.NORMAL,
                        0,
                        0xf000f0
                );
            } catch (Exception e) {
                UChat.chat("Failed to render text: " + e.getMessage());
            }

            offsetY += fontRenderer.fontHeight + 1;
        }
        matrixStack.pop();
    }

    private static TextSplitResult splitText(String text) {
        MinecraftClient mc = MinecraftClient.getInstance();
        TextRenderer fontRenderer = mc.textRenderer;

        String[] splitLines = text.split("\n");
        List<String> lines = new ArrayList<>();

        for (String line : splitLines) {
            lines.add(line);
        }

        float maxWidth = 0;
        for (String line : lines) {
            float lineWidth = fontRenderer.getWidth(line);
            if (lineWidth > maxWidth) {
                maxWidth = lineWidth;
            }
        }

        float height = lines.size() * (fontRenderer.fontHeight + 1) - 1;

        return new TextSplitResult(lines, maxWidth, height);
    }

    private static class TextSplitResult {
        final List<String> lines;
        final float width;
        final float height;

        TextSplitResult(List<String> lines, float width, float height) {
            this.lines = lines;
            this.width = width;
            this.height = height;
        }
    }

    public static int getColor(int red, int green, int blue, int alpha) {
        return ((alpha & 0xFF) << 24) |
                ((red & 0xFF) << 16) |
                ((green & 0xFF) << 8) |
                (blue & 0xFF);
    }

    public static int getColor(int red, int green, int blue) {
        return getColor(red, green, blue, 255);
    }
}